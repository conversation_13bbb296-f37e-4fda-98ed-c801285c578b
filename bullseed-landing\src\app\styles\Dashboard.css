/* Dashboard Styles - Modern Dark Theme */

/* Global overflow prevention */
* {
  box-sizing: border-box;
}

/* Dashboard Loading State */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-loading p {
  font-size: 16px;
  font-weight: 500;
}

/* Main Container */
.dashboard-container {
  min-height: 100vh;
  background: #0a0a0a;
  color: white;
  padding: 16px;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  width: 100%;
  margin: 0 auto;
}

.dashboard-content {
  max-width: 100%;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}



/* Market Ticker Bar */
.market-ticker-bar {
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 0;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

/* Visual indicator for data updates */
.market-ticker-bar.data-updated {
  background: rgba(0, 255, 0, 0.1);
  border-bottom-color: rgba(0, 255, 0, 0.3);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.2);
}

/* LIVE indicator */
.ticker-live-indicator {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 6px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #00ff00;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.live-text {
  color: #00ff00;
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 1px;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
  margin: -24px -24px 24px -24px;
}

.ticker-container {
  width: 100%;
  overflow: hidden;
}

.ticker-scroll {
  display: flex;
  animation: scroll-ticker 60s linear infinite;
  gap: 40px;
  white-space: nowrap;
}

@keyframes scroll-ticker {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

.ticker-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 20px;
  flex-shrink: 0;
}

.ticker-symbol {
  color: white;
  font-weight: 600;
  font-size: 14px;
  min-width: 60px;
}

.ticker-price {
  color: #E5E5E5;
  font-size: 14px;
  font-weight: 500;
  min-width: 80px;
}

.ticker-change {
  font-size: 12px;
  font-weight: 500;
  min-width: 60px;
}

.ticker-change.positive {
  color: #00D4AA;
}

.ticker-change.negative {
  color: #EF4444;
}

.dashboard {
  padding: 24px;
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  background: #0a0a0a;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow-x: hidden;
  box-sizing: border-box;
}

.dashboard-header {
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-welcome h1 {
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
  letter-spacing: -0.02em;
}

.dashboard-welcome p {
  font-size: 14px;
  color: #6b7280;
  font-weight: 400;
}

.dashboard-header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.date-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.date-selector:hover {
  background: #1a1a1a;
  border-color: #444444;
}

.date-selector svg {
  color: #6b7280;
}

.date-picker-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #1a1a1a;
  border: 1px solid #333333;
  border-radius: 8px;
  margin-top: 4px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.date-picker-dropdown div {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #333333;
}

.date-picker-dropdown div:last-child {
  border-bottom: none;
}

.date-picker-dropdown div:hover {
  background: #2a2a2a;
}

.add-new-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #22c55e;
  border: none;
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-new-btn:hover {
  background: #16a34a;
}

.dashboard-content {
  display: grid;
  gap: 12px;
  grid-template-columns: minmax(250px, 1fr) minmax(400px, 2fr) minmax(250px, 1fr);
  grid-template-rows: auto auto;
  grid-template-areas:
    "stats chart credit"
    "history history history";
  max-width: 100%;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
}

/* Stats Section - Left Column */
.dashboard-stats {
  grid-area: stats;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0;
  overflow: hidden;
}

.stat-card {
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: #2a2a2a;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.02em;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  color: #22c55e;
}

.stat-change.negative {
  color: #ef4444;
}

.stat-change-icon {
  font-size: 10px;
}

.download-report-btn {
  background: #10b981;
  border: none;
  color: #ffffff;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
}

.download-report-btn:hover:not(:disabled) {
  background: #16a34a;
  transform: translateY(-1px);
}

.download-report-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Trading Chart - Center Column */
.dashboard-chart {
  grid-area: chart;
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
  min-width: 0;
  overflow: hidden;
}

.dashboard-chart:hover {
  border-color: #2a2a2a;
}

.trading-chart {
  width: 100%;
  height: 380px;
}

.trading-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.trading-chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.chart-coin-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-coin-select {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.chart-coin-select:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.chart-coin-select:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

.chart-coin-select option {
  background: #1a1a1a;
  color: white;
  padding: 8px;
}

.trading-chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chart-timeframes {
  display: flex;
  gap: 2px;
  background: #1a1a1a;
  border-radius: 8px;
  padding: 2px;
}

.chart-timeframe {
  background: none;
  border: none;
  color: #6b7280;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-timeframe.active {
  background: #ffffff;
  color: #000000;
}

.chart-timeframe:hover:not(.active) {
  background: #2a2a2a;
  color: #ffffff;
}

/* Enhanced Chart Container */
.chart-container {
  position: relative;
  width: 100%;
  height: 380px;
  background: #131722;
  border-radius: 6px;
  min-width: 0;
  overflow: hidden;
  border: 1px solid #2a2a2a;
}

/* Chart Data Panel */
.chart-data-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  z-index: 5;
  font-size: 12px;
  transition: all 0.3s ease;
}

.chart-data-panel.minimized {
  width: 40px;
  height: 40px;
}

.chart-panel-header {
  display: flex;
  justify-content: flex-end;
  padding: 8px;
}

.chart-panel-toggle {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.chart-panel-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.chart-data-panel:not(.minimized) {
  padding: 12px;
  min-width: 180px;
}
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 20px;
}

.chart-data-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  min-width: 180px;
  backdrop-filter: blur(10px);
  z-index: 5;
  font-size: 12px;
}

.chart-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.metric-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 12px;
  color: #8B8B8B;
  font-weight: 500;
}

.metric-value {
  font-size: 14px;
  color: white;
  font-weight: 600;
}

.chart-timeframes {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.timeframe-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #8B8B8B;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 24px;
  text-align: center;
}

.timeframe-btn.active {
  background: #00D4AA;
  color: white;
  border-color: #00D4AA;
}

.timeframe-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.trading-chart-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-price-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.price-change {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
}

.price-change.positive {
  color: #00d4aa;
  background: rgba(0, 212, 170, 0.1);
}

.price-change.negative {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.metric-value.positive {
  color: #00d4aa;
}

.metric-value.negative {
  color: #ef4444;
}

.chart-price-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.chart-price-dot.btc {
  background: #F7931A;
}

.chart-price-dot.eth {
  background: #627EEA;
}

.chart-symbol {
  font-size: 11px;
  color: #8B8B8B;
  font-weight: 500;
}

.price-current {
  font-size: 13px;
  color: white;
  font-weight: 600;
}

.chart-tools {
  display: flex;
  gap: 8px;
}

.chart-tool {
  background: none;
  border: 1px solid #2a2a2a;
  color: #6b7280;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-tool:hover {
  background: #1a1a1a;
  border-color: #3a3a3a;
  color: #ffffff;
}

.trading-chart-info {
  display: flex;
  gap: 24px;
  align-items: center;
}

.chart-price-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-price-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.chart-price-dot.btc {
  background: #f7931a;
}

.chart-price-dot.eth {
  background: #627eea;
}

.chart-symbol {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.price-current {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-left: 4px;
}

.trading-chart-container {
  position: relative;
  height: 380px;
  background: #131722;
  border-radius: 6px;
  overflow: hidden;
}

.trading-chart-canvas {
  width: 100%;
  height: 100%;
}
}

.chart-indicators {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.chart-market-data {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.market-data-item {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  font-size: 12px;
}

.market-data-label {
  color: rgba(255, 255, 255, 0.6);
}

.market-data-value {
  color: white;
  font-weight: 600;
}

/* Portfolio Performance - Right Column */
.dashboard-portfolio {
  grid-area: credit;
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  min-width: 0;
  overflow: hidden;
  flex-direction: column;
}

.portfolio-header {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 24px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.portfolio-header-info {
  position: relative;
  display: inline-block;
}

.info-icon {
  font-size: 12px;
  cursor: help;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.info-icon:hover {
  opacity: 1;
}

.info-tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 400;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 4px;
  max-width: 200px;
  white-space: normal;
  text-align: center;
}

.portfolio-header-info:hover .info-tooltip {
  opacity: 1;
  visibility: visible;
}



.portfolio-circle {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: conic-gradient(from 0deg, #22c55e 0deg 288deg, #1f1f1f 288deg 360deg);
  border-radius: 50%;
  padding: 8px;
}

.portfolio-circle::before {
  content: '';
  position: absolute;
  inset: 8px;
  background: #111111;
  border-radius: 50%;
}

.portfolio-value {
  position: relative;
  z-index: 1;
  font-size: 36px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
}

.portfolio-label {
  position: relative;
  z-index: 1;
  font-size: 14px;
  color: #22c55e;
  font-weight: 500;
}

.portfolio-info {
  text-align: center;
  margin-bottom: 16px;
}

/* Portfolio Insights */
.portfolio-insights {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.insight-item.positive {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.insight-item.negative {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.insight-item.suggestion {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.insight-item.milestone {
  background: rgba(168, 85, 247, 0.1);
  border-color: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.insight-icon {
  font-size: 14px;
}

.insight-message {
  font-weight: 500;
}

.portfolio-date {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.portfolio-status {
  font-size: 14px;
  color: #ffffff;
}

.portfolio-cryptos {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
}

.portfolio-crypto-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.portfolio-crypto-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Animation for data updates */
.portfolio-crypto-item.data-updated {
  background: rgba(0, 255, 0, 0.1);
  border-color: rgba(0, 255, 0, 0.3);
  transform: scale(1.02);
}

.crypto-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.crypto-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.portfolio-crypto-item .crypto-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
}
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.crypto-icon.btc {
  background: #F7931A;
}

.crypto-icon.eth {
  background: #627EEA;
}

.crypto-icon.ada {
  background: #0033AD;
}

.crypto-icon.sol {
  background: linear-gradient(135deg, #9945FF, #14F195);
}

.crypto-icon.link {
  background: #375BD2;
}

.crypto-label {
  font-size: 13px;
  color: #E5E5E5;
  font-weight: 500;
}

.crypto-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.crypto-value {
  font-size: 14px;
  color: white;
  font-weight: 600;
}

.crypto-change {
  font-size: 11px;
  font-weight: 500;
}

.crypto-change.positive {
  color: #00D4AA;
}

.crypto-change.negative {
  color: #EF4444;
}

/* NFT Section */
.portfolio-nft-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nft-header {
  font-size: 12px;
  color: #8B8B8B;
  font-weight: 600;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nft-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.01);
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.nft-item:hover {
  background: rgba(255, 255, 255, 0.03);
}

.nft-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nft-icon {
  font-size: 16px;
}

.nft-label {
  font-size: 12px;
  color: #E5E5E5;
  font-weight: 500;
}

.nft-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.nft-value {
  font-size: 12px;
  color: white;
  font-weight: 600;
}

.nft-change {
  font-size: 10px;
  font-weight: 500;
}

.nft-change.positive {
  color: #00D4AA;
}

.nft-change.negative {
  color: #EF4444;
}

.bitcoin-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bitcoin-icon {
  width: 24px;
  height: 24px;
  background: #f7931a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
}

.bitcoin-label {
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
}

.bitcoin-value {
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
}

.bitcoin-change {
  font-size: 12px;
  color: #22c55e;
  font-weight: 500;
}

/* Balance Card - Second Row */
.dashboard-balance {
  grid-area: balance;
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  transition: all 0.2s ease;
  overflow: hidden;
}

.dashboard-balance:hover {
  border-color: #2a2a2a;
}

.balance-card-header {
  background: linear-gradient(135deg, #10b981, #059669);
  padding: 24px;
}

.balance-available-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.balance-available-amount {
  font-size: 36px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.balance-card-body {
  padding: 24px;
}

.balance-withdrawal {
  margin-bottom: 24px;
}

.balance-withdrawal-header {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 8px;
}

.balance-withdrawal-amount {
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.balance-breakdown {
  margin-bottom: 24px;
}

.balance-breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  font-size: 14px;
  border-bottom: 1px solid #1f1f1f;
}

.balance-breakdown-item:last-child {
  border-bottom: none;
}

.balance-breakdown-label {
  color: #9ca3af;
  font-weight: 500;
}

.balance-breakdown-value {
  color: #ffffff;
  font-weight: 600;
}

.balance-breakdown-item.total {
  font-weight: 700;
  font-size: 16px;
  border-top: 2px solid #1f1f1f;
  padding-top: 16px;
  margin-top: 16px;
}

.balance-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.balance-action-btn {
  padding: 14px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 14px;
}

.balance-action-btn.primary {
  background: #10b981;
  color: #ffffff;
}

.balance-action-btn.primary:hover {
  background: #059669;
  transform: translateY(-1px);
}

.balance-action-btn.secondary {
  background: transparent;
  color: #9ca3af;
  border: 1px solid #2a2a2a;
}

.balance-action-btn.secondary:hover {
  background: #1a1a1a;
  color: #ffffff;
  border-color: #3a3a3a;
}

/* Market Section - Second Row Right */
.dashboard-market {
  grid-area: market;
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease;
}

.dashboard-market:hover {
  border-color: #2a2a2a;
}

.market-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.market-header h3 {
  font-size: 14px;
  font-weight: 500;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.market-reward {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.market-price {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.market-currency {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.market-value {
  font-size: 28px;
  font-weight: 700;
  color: white;
}

.market-change {
  font-size: 14px;
  font-weight: 600;
}

.market-change.positive {
  color: #00d4aa;
}

/* Referral Card */
.dashboard-referral {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.referral-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.referral-card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.referral-invite-btn {
  background: rgba(71, 85, 105, 1);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.referral-invite-btn:hover {
  background: rgba(71, 85, 105, 0.8);
}

.referral-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 16px;
}

.referral-link-container {
  margin-bottom: 20px;
}

.referral-link {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.referral-link-text {
  flex: 1;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.referral-copy-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.referral-copy-btn:hover {
  background: rgba(0, 212, 170, 0.15);
}

.referral-copy-btn.copied {
  background: rgba(0, 212, 170, 0.2);
  border-color: rgba(0, 212, 170, 0.5);
}

.referral-stats {
  display: flex;
  gap: 20px;
}

.referral-stat {
  flex: 1;
}

.referral-stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.referral-stat-value {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.referral-stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* Market Ticker */
.dashboard-ticker {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.market-ticker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.market-ticker-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.market-ticker-powered {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.market-ticker-scroll {
  overflow-x: auto;
  margin-bottom: 16px;
}

.market-ticker-items {
  display: flex;
  gap: 16px;
  min-width: max-content;
}

.market-ticker-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  min-width: 200px;
}

.market-ticker-symbol {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.ticker-symbol {
  font-size: 16px;
  font-weight: 700;
  color: white;
}

.ticker-pair {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.market-ticker-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.ticker-price {
  font-size: 18px;
  font-weight: 700;
  color: white;
}

.ticker-change {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.ticker-change.positive {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.ticker-change.negative {
  background: rgba(220, 38, 38, 0.2);
  color: #dc2626;
}

.market-ticker-volume {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.ticker-volume-label {
  color: rgba(255, 255, 255, 0.6);
}

.ticker-volume-value {
  color: white;
  font-weight: 600;
}

.market-ticker-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.ticker-control-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ticker-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.market-ticker-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.6);
}

/* Payment History */
.payment-history {
  grid-area: history;
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 24px;
}

.payment-history h3 {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 24px;
}

.payment-history-table {
  width: 100%;
  box-sizing: border-box;
}

.payment-history-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 16px;
  align-items: center;
  box-sizing: border-box;
}

.payment-history-header .payment-col {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  min-height: 40px;
}

.payment-history-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-history-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  align-items: center;
  box-sizing: border-box;
}

.payment-crypto {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.payment-crypto-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
  font-size: 14px;
}

.payment-crypto-icon.ada {
  background: #0033ad;
}

.payment-crypto-icon.cardano {
  background: #0033ad;
}

.payment-crypto-icon.digibyte {
  background: #006ad4;
}

.payment-crypto-icon.ethereum {
  background: #627eea;
}

.payment-crypto span {
  color: white;
  font-weight: 600;
}

.payment-col {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  display: flex;
  align-items: center;
  min-height: 40px;
  overflow: hidden;
}

.payment-history-row .payment-col:nth-child(2),
.payment-history-row .payment-col:nth-child(3),
.payment-history-row .payment-col:nth-child(4) {
  justify-content: flex-start;
}

.payment-history-header .payment-col:nth-child(2),
.payment-history-header .payment-col:nth-child(3),
.payment-history-header .payment-col:nth-child(4) {
  justify-content: flex-start;
}

.payment-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.payment-status.success {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.payment-status.pending {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.payment-status.failed {
  background: rgba(220, 38, 38, 0.2);
  color: #dc2626;
}

/* Mobile Responsive */
@media (max-width: 1400px) {
  .dashboard-content {
    grid-template-columns: minmax(200px, 1fr) minmax(350px, 2fr) minmax(200px, 1fr);
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: minmax(200px, 1fr) minmax(300px, 2fr);
    grid-template-areas:
      "stats chart"
      "credit credit"
      "history history";
    gap: 16px;
  }

  .dashboard-row:first-child {
    grid-template-columns: 1fr;
  }

  .dashboard-row:nth-child(2) {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1100px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    grid-template-areas:
      "stats"
      "chart"
      "credit"
      "history";
    gap: 16px;
  }
}

@media (max-width: 1024px) {
  .dashboard {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .dashboard-welcome h1 {
    font-size: 24px;
  }
  
  .dashboard-content {
    gap: 16px;
  }
  
  .dashboard-row {
    gap: 16px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .payment-history-header,
  .payment-history-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .payment-history-header {
    display: none;
  }
  
  .payment-history-row {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border-bottom: none;
  }

  .dashboard-welcome p {
    font-size: 14px;
  }

  .dashboard-header-controls {
    flex-direction: column;
    gap: 12px;
  }

  .dashboard-content {
    grid-template-columns: 1fr;
    grid-template-areas:
      "stats"
      "chart"
      "credit"
      "history";
  }

  .chart-container {
    height: 300px;
  }

  .coin-selection-grid {
    grid-template-columns: 1fr;
  }
}

/* Large screen adjustments */
@media (min-width: 1600px) {
  .dashboard {
    max-width: 1400px;
    margin: 0 auto;
  }

  .dashboard-content {
    gap: 20px;
    grid-template-columns: minmax(280px, 1fr) minmax(500px, 2fr) minmax(280px, 1fr);
  }
}

/* Coming Soon Styles */
.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.coming-soon-icon {
  margin-bottom: 24px;
  color: #10b981;
  opacity: 0.7;
}

.coming-soon h2 {
  font-size: 24px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.coming-soon p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  max-width: 400px;
  line-height: 1.5;
}

/* Loading Spinner */
.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.payment-history-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.7);
}

.payment-history-loading .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.payment-history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.payment-history-empty p {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: white;
}

.payment-history-empty span {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
}

/* Payment History - Full Width Bottom Section */
.dashboard-payment-history {
  grid-area: history;
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease;
  overflow-x: auto;
  min-width: 0;
}

.dashboard-payment-history:hover {
  border-color: #2a2a2a;
}

.payment-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.payment-history-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.payment-history-table-wrapper {
  overflow-x: auto;
  margin: 0;
  padding: 0;
}

.payment-history-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px;
}

.payment-history-table th {
  text-align: left;
  padding: 12px 16px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #1f1f1f;
}

.payment-history-table td {
  padding: 16px;
  border-bottom: 1px solid #1a1a1a;
  font-size: 14px;
}

.payment-history-table tr:hover {
  background: #0f0f0f;
}

.payment-crypto {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-crypto-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.payment-crypto-icon.achain { background: #4f46e5; }
.payment-crypto-icon.cardano { background: #0033ad; }
.payment-crypto-icon.digibyte { background: #006ad4; }
.payment-crypto-icon.ethereum { background: #627eea; }

.payment-crypto-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.payment-crypto-name {
  font-weight: 600;
  color: #ffffff;
}

.payment-crypto-change {
  font-size: 12px;
  font-weight: 500;
}

.payment-crypto-change.positive {
  color: #10b981;
}

.payment-crypto-change.negative {
  color: #ef4444;
}

.payment-date {
  color: #9ca3af;
  font-weight: 500;
}

.payment-price {
  font-weight: 600;
  color: #ffffff;
}

.payment-status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.payment-status.success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.payment-status.pending {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.payment-status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: #1a1a1a;
  border: 1px solid #333333;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #333333;
}

.modal-header h3 {
  margin: 0;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #333;
  color: white;
}

.modal-body {
  padding: 24px;
}

.modal-body p {
  margin: 0 0 20px 0;
  color: #ccc;
}

.coin-selection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

/* Modal Improvements */
.current-portfolio {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.current-portfolio h4,
.available-coins h4 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.current-coins {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.current-coin-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
}

.current-coin-item .coin-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.current-coin-item .coin-name {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.remove-coin-btn {
  background: rgba(220, 38, 38, 0.2);
  border: 1px solid rgba(220, 38, 38, 0.3);
  color: #dc2626;
  border-radius: 4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.remove-coin-btn:hover {
  background: rgba(220, 38, 38, 0.3);
  border-color: rgba(220, 38, 38, 0.5);
}

.available-coins {
  margin-top: 20px;
}

.coin-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.coin-option:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.08);
  border-color: #00d4aa;
}

.coin-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.coin-option .coin-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.coin-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.coin-info {
  flex: 1;
}

.coin-name {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.coin-symbol {
  color: #888;
  font-size: 12px;
  text-transform: uppercase;
}

.coin-price {
  color: #22c55e;
  font-weight: 600;
  font-size: 14px;
}
