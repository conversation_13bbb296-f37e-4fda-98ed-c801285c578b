/* Profile Page Styles */
.profile {
  padding: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Profile Loading State */
.profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: rgba(255, 255, 255, 0.7);
}

.profile-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.profile-loading p {
  font-size: 16px;
  font-weight: 500;
}

.profile-header {
  margin-bottom: 32px;
}

.profile-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.profile-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.profile-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
}

.profile-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.profile-card-header {
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.profile-avatar-section {
  display: flex;
  gap: 16px;
  align-items: center;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.profile-avatar-info h2 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.profile-avatar-info p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 12px;
}

.profile-avatar-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.profile-avatar-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.profile-actions {
  display: flex;
  gap: 12px;
}

.profile-edit-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.profile-edit-btn:hover {
  background: rgba(0, 212, 170, 0.15);
}

.profile-edit-actions {
  display: flex;
  gap: 8px;
}

.profile-save-btn {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.profile-cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-cancel-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.profile-form {
  padding: 24px;
}

.profile-section {
  margin-bottom: 32px;
}

.profile-section:last-child {
  margin-bottom: 0;
}

.profile-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.profile-form-group {
  display: flex;
  flex-direction: column;
}

.profile-form-group.full-width {
  grid-column: 1 / -1;
}

.profile-form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.profile-form-group input,
.profile-form-group select {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.profile-form-group input:focus,
.profile-form-group select:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.profile-form-group input:disabled,
.profile-form-group select:disabled {
  background: rgba(255, 255, 255, 0.02);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.profile-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.profile-stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 16px;
}

.profile-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  background: rgba(0, 212, 170, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00d4aa;
  flex-shrink: 0;
}

.profile-stat-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.profile-stat-info p {
  font-size: 18px;
  font-weight: 700;
  color: white;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .profile {
    padding: 16px;
  }
  
  .profile-header h1 {
    font-size: 24px;
  }
  
  .profile-card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-actions {
    justify-content: center;
  }
  
  .profile-form {
    padding: 16px;
  }
  
  .profile-form-grid {
    grid-template-columns: 1fr;
  }
  
  .profile-stat-card {
    padding: 16px;
  }
  
  .profile-stat-icon {
    width: 40px;
    height: 40px;
  }
}
