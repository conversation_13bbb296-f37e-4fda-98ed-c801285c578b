/* Authentication Pages Styles */
.auth-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
  overflow: hidden;
}

.auth-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 60px;
  background:
    linear-gradient(135deg, #00d4aa 0%, #00b894 50%, #009688 100%),
    radial-gradient(circle at top right, rgba(255,255,255,0.1) 0%, transparent 50%);
  position: relative;
  overflow: hidden;
  position: fixed;
  left: 0;
  top: 0;
  width: 50%;
  height: 100vh;
}

.auth-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255,255,255,0.15) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.15) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.15) 75%);
  background-size: 80px 80px;
  background-position: 0 0, 0 40px, 40px -40px, -40px 0px;
  opacity: 1;
}

.auth-left::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 170, 0.6) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(0, 184, 148, 0.5) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.2) 0%, transparent 40%),
    linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 184, 148, 0.1) 100%);
  opacity: 1;
}

.auth-left .grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 1;
  opacity: 0.6;
}







.auth-brand {
  position: relative;
  z-index: 3;
  margin-bottom: 60px;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 12px;

}

.logo-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.logo-image {
  width: 48px;
  height: 48px;
  object-fit: contain;
  border-radius: 12px;
}

.brand-name {
  font-size: 28px;
  font-weight: 700;
  color: white;
  letter-spacing: -0.5px;
}

.auth-welcome {
  position: relative;
  z-index: 3;
}

.auth-welcome h1 {
  font-size: 48px;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  line-height: 1.2;
  letter-spacing: -1px;
}

.auth-welcome p {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 400px;
}

.auth-switch {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.auth-link {
  color: white;
  text-decoration: underline;
  font-weight: 600;
  transition: opacity 0.2s ease;
}

.auth-link:hover {
  opacity: 0.8;
}

.auth-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 40px 40px 40px;
  background: #0f0f0f;
  margin-left: 50%;
  width: 50%;
  overflow-y: auto;
  height: 100vh;
}

.auth-form-container {
  width: 100%;
  max-width: 420px;
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-header h2 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
  letter-spacing: -0.5px;
}

.auth-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.form-group input {
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 16px;
  transition: all 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #00d4aa;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-top: 8px;
}

.form-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin-top: 2px;
  accent-color: #00d4aa;
}

.form-checkbox label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.terms-link {
  color: #00d4aa;
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  text-decoration: underline;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: -8px;
}

.forgot-password-btn {
  background: none;
  border: none;
  color: #00d4aa;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  transition: opacity 0.2s ease;
}

.forgot-password-btn:hover {
  opacity: 0.8;
}

.error-message {
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  color: #fca5a5;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  text-align: center;
}

.auth-submit-btn {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  border: none;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.auth-submit-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.auth-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: 32px 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.auth-divider span {
  padding: 0 16px;
}

.social-auth {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 14px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.social-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.social-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading Styles */
.auth-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #0f0f0f;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 170, 0.3);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Terms and Privacy Pages */
.terms-content {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
}

.terms-content::-webkit-scrollbar {
  width: 6px;
}

.terms-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.terms-content::-webkit-scrollbar-thumb {
  background: #00d4aa;
  border-radius: 3px;
}

.terms-content h2 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 24px;
  text-align: center;
}

.terms-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.terms-section:last-of-type {
  border-bottom: none;
}

.terms-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #00d4aa;
  margin-bottom: 8px;
}

.terms-section p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.terms-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;
    overflow: visible;
  }

  .auth-left {
    position: relative;
    width: 100%;
    height: auto;
    padding: 40px 20px;
    min-height: 300px;
  }

  .auth-welcome h1 {
    font-size: 36px;
  }

  .auth-welcome p {
    font-size: 16px;
  }

  .auth-right {
    position: relative;
    margin-left: 0;
    width: 100%;
    height: auto;
    padding: 20px;
    overflow-y: visible;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}
